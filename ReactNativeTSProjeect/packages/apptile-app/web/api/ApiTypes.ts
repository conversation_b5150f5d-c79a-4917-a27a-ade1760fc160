import {Plan} from 'apptile-core';
import {ISourcePlatformType} from '../../../apptile-core/common/datatypes/types';

export type LoginUserResponse = {
  accessToken: string;
};

export type LogoutResponse = {
  success: boolean;
  message: string;
};

export type App = {
  id: number;
  name: string;
};

export type IOrganization = {
  id: string;
  name: string;
  apps?: App[];
};

export type FetchOrganizationsResponse = IOrganization[];

export type FetchNormalizedOrg = Omit<IOrganization, 'apps'> & {apps: string[]};

export type FetchOrgsNormalizedPayload = {
  result: [string];
  entities: {
    org: {
      [key: string]: FetchNormalizedOrg;
    };
  };
};

export interface IAppBranch {
  id: number;
  forkId: number;
  branchName: string;
  title: string;
  headCommitId: number;
  previousCommitId: number;
  createdAt: string;
  updatedAt: string;
}
export type FetchAppBranchesResponse = IAppBranch[];
export type FetchAppBranchesNormalizedPayload = {
  result: [string];
  entities: {
    branch: {
      [key: string]: IAppBranch;
    };
  };
};

export interface IAppFork {
  id: number;
  forkName: string;
  title: string;
  frameworkVersion: number;
  publishedCommitId: number;
  createdAt: string;
  updatedAt: string;
}
export interface FetchAppForksResponse {
  forks: IAppFork[];
}
export type FetchAppForksNormalizedPayload = {
  result: [string];
  entities: {
    fork: {
      [key: string]: IAppFork;
    };
  };
};

export interface IAppAsset {
  id: string;
  appId: string;
  parentId: string;
  fileName: string;
  thumbUrl: string;
  fileUrl: string;
  width: number;
  height: number;
  variants?: string[];
}

export type FetchIAppAssetNormalizedResponse = {
  result: [string] | string;
  entities: {
    items: {
      [key: string]: IAppAsset;
    };
  };
};

export interface FetchUserResponse {
  id: string;
  firstname: string;
  lastname: string;
  email: string;
  role: string;
  contactNumber: string;
  betaAccessEnabled?: boolean;
}

export interface UpdateUserBody {
  firstname: string;
  lastname: string;
  role: string;
  contactNumber: string;
}

export interface FetchPaymentURLResponse {
  confirmationUrl: string;
}

export interface IPlanFeature {
  id: string;
  name: string;
  type: string;
  description: string;
  featureCode: string;
}

export interface ISubscriptionPlan {
  id: string;
  name: string;
  trialPeriodDays: number;
  sequence: number;
  description: string;
  featureDescription: string;
  monthlyPrice: string;
  monthlyPriceDiscount: number;
  displayPrice: number;
  strikeoffPrice: number;
  annualPrice: number;
  annualPriceDiscount: number;
  currencyCode: string;
  mostPopular: boolean;
  topFeatures: IPlanFeature[];
  features: IPlanFeature[];
  createdAt: Date;
}
export interface ISelectedPlan extends ISubscriptionPlan {
  planFeatureByType: Record<string, IPlanFeature[]>;
}

export interface IPlanListing extends ISubscriptionPlan {
  isCurrentPlan: boolean;
  isPaid: boolean;
}

export type FetchNormalizedResponse<T> = {
  result: [string] | string;
  entities: {
    items: {
      [key: string]: T;
    };
  };
};

export enum ESubscriptionState {
  ACTIVE = 'ACTIVE',
  CANCELLED = 'CANCELLED',
  DECLINED = 'DECLINED',
  EXPIRED = 'EXPIRED',
  FROZEN = 'FROZEN',
  PENDING = 'PENDING',
}

export interface ICurrentSubscription {
  id: string;
  planId: string;
  subscriberOrganizationId: string;
  subscriptionState: ESubscriptionState;
  billingInterval: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date;
  isLatest: boolean;
  planMonthlyPrice: number;
  monthlyPriceDiscount: number;
  planAnnualPrice: number;
  annualPriceDiscount: number;
  currencyCode: string;
}

export interface ICurrentApp {
  id: string;
  name: string;
  uuid: string;
  organizationId: string;
}

export interface IFetchIntegrationResponse {
  id: string;
  title: string;
  description: string;
  excerpt: string;
  tags: string[];
  integrationCode: string;
  icon: string;
  images: string[];
  features: string[];
  status: string;
  isPremium: boolean;
  selfServe: boolean;
  monthlyPrice: number;
  minimumPlan: string;
  currencyCode: string;
}

export interface IFetchAppIntegrationResponse {
  id: string;
  organizationId: string;
  credentials: Record<string, any>;
  platformIdentifier: string;
  platformType: string;
  active: boolean;
  tokenRefreshRequired: boolean;
  deletedAt: any;
  createdAt: string;
  updatedAt: string;
}

export interface ITileSaveInterface {
  id: string;
  name: string;
  description?: string;
  coverImage?: string;
  assets?: string[];
  remark?: string;
  tags: string[];
  data: string;
  navigationInfo: any;
  integrationName: string;
  oldId?: string;
  secretCode?: string;
  variants?: string;
  sourcePlatformType?: ISourcePlatformType;
}

export interface ITileVariantSaveInterface {
  id?: string;
  name: string;
  description?: string;
  coverImage: string;
  tileId: string;
  data: string;
  hidden?: boolean;
  gating?: Plan;
  secretCode?: string;
}

export type ITileUpdateInterface = Partial<ITileSaveInterface> & Pick<ITileSaveInterface, 'id'>;
export type ITileItem = ITileSaveInterface;

export interface ITilesListResponse {
  items: ITileItem[];
  totalCount: number;
  offset: number;
  limit: number;
  nextPageAvailable: boolean;
}

export interface IOnboardingResponse {
  id: number;
  appId: string;
  metadata: Record<string, any>;
}

interface Format {
  src: string;
  background: string;
  format: string;
  height: number;
  width: number;
  size: number;
}

interface IFont {
  name: string;
  type: string;
  origin: string;
  originId: null;
  weights: any[];
}

interface IColor {
  hex: string;
  type: string;
  brightness: number;
}

interface IImage {
  formats: Format[];
  tags: any[];
  type: string;
}

interface ILogos extends IImage {
  theme: string;
}

export interface IBrandResponse {
  logos: Array<ILogos>;
  colors: Array<IColor>;
  fonts: Array<IFont>;
  images: Array<IImage>;
  storeLeads?: any;
  sales?: {month: string; year: number; amount: number; currencyCode?: string};
}

export interface IAppOnlyDiscountAttributes {
  id: string;
  discountId: string;
  appliesOncePerCustomer: boolean;
  code: string;
  combinesWith: ICombinesWith;
  endsAt: string | null;
  functionId: string;
  metafields?: IMetafield[];
  startsAt: string;
  title: string;
  usageLimit: number | null;
  status: DiscountStatusEnum;
  totalSales: number;
  currencyCode: string;
}

export interface IAppOnlyDiscountCreateAttributes {
  appliesOncePerCustomer: boolean;
  code: string;
  combinesWith: ICombinesWith;
  endsAt: string | null;
  metafields?: IMetafield[];
  startsAt: string;
  title: string;
  usageLimit: number | null;
}

export interface IApptileCartUpsellAttributes {
  id: string;
  appId: string;
  shopName: string | null;
  discountId: string;
  discountClass: string;
  combinesWith: ICombinesWith;
  endsAt?: string | null;
  functionId: string;
  metafields?: ICartUpsellMetafield[];
  startsAt: string;
  title: string;
  status: DiscountStatusEnum;
}

export interface IApptileCartUpsellPayload {
  endsAt?: string | null;
  startsAt: string;
  title: string;
}

// interface ITargetItemImage {
//   url: string;
//   id: string;
//   width: number;
//   height: number;
//   altText: string;
// }
export interface ICombinesWith {
  orderDiscounts: boolean;
  productDiscounts: boolean;
  shippingDiscounts: boolean;
}

export enum CombinesWith {
  ORDER_DISCOUNT = 'orderDiscounts',
  PRODUCT_DISCOUNT = 'productDiscounts',
  SHIPPING_DISCOUNT = 'shippingDiscounts',
}

export enum DiscountStatusEnum {
  ACTIVE = 'ACTIVE',
  EXPIRED = 'EXPIRED',
  DELETED = 'DELETED',
  SCHEDULED = 'SCHEDULED',
}

export interface IMetafield {
  description?: string;
  id?: string;
  key?: string;
  namespace: string;
  type: string;
  value: string;
}

export interface ICartUpsellMetafield {
  key?: string;
  namespace: string;
  type: string;
  value: string;
}

export interface ICartUpsellMetafieldValue {
  targetSourceName: string;
  discounts: ICartUpsellDiscount[];
  onlyInApp: boolean;
}
export interface ICartUpsellDiscount {
  type: 'percentage' | 'fixedAmount';
  value: number;
  minAmount: number;
}

export interface IMetafieldValue {
  targetSourceName: string;
  discounts: MetaDiscount[];
  discountApplicationStrategy: string;
}

interface MetaDiscount {
  conditions: [
    {
      orderMinimumSubtotal: {
        excludedVariantIds: string[];
        minimumAmount: number;
        targetType: DiscountTargetType;
      };
    },
  ];
  value: {
    [DiscountValueTypeEnum.PERCENTAGE]?: {value: number};
    [DiscountValueTypeEnum.FIXED_AMOUNT]?: {amount: number};
  };
  message: string;
  targets: Target[];
}
interface Target {
  orderSubtotal: OrderSubtotal;
}
interface OrderSubtotal {
  excludedVariantIds: any[];
}

export enum DiscountValueTypeEnum {
  FIXED_AMOUNT = 'fixedAmount',
  PERCENTAGE = 'percentage',
}

export enum DiscountTargetType {
  ORDER_SUBTOTAL = 'ORDER_SUBTOTAL',
  PRODUCT_VARIANT = 'PRODUCT_VARIANT',
}

export enum OutcomeTimeRangeEnum {
  THIRTY_DAYS = '1mo',
  SIXTY_MINUTES = '1h',
  TWENTY_FOUR_HOUR = '1d',
}

export interface IAppBranchesWithScheduledOta extends IAppBranch {
  scheduledOtas: ScheduledOta[];
}
export type IAppBranchesWithScheduledOtaResponse = IAppBranchesWithScheduledOta[];
export interface ISnapshot {
  id: string;
  createdBy: string;
  forkId: number;
  branchId: number;
  startDate: Date;
  endDate?: Date;
  scheduledOtas: ScheduledOta[];
}

export type SnapshotResponse = ISnapshot[];
export interface ScheduledOta {
  id: number;
  snapshotId: string;
  forkId: number;
  branchId: number;
  publishDate: string;
  targetCommitId?: number;
  revertSnapshot: boolean;
  status: ScheduledOtaStatusEnum;
  createdAt: string;
  updatedAt: string;
  deletedAt: any;
}

export enum ScheduledOtaStatusEnum {
  PENDING = 'PENDING',
  EXECUTED = 'EXECUTED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
}

export interface ICloudinaryVideo {
  asset_id: string;
  public_id: string;
  format: string;
  version: number;
  resource_type: string;
  type: string;
  created_at: string;
  bytes: number;
  width: number;
  height: number;
  folder: string;
  url: string;
  secure_url: string;
  thumbnail: string;
}

export type FetchCloudinaryVideosResponse = {
  resources: ICloudinaryVideo[];
  next_cursor: string;
};

export interface IStoreCreditCreateSignUpEventRuleData {
  creditAmount: number;
  creditExpiryDays?: number;
  currencyCode: string;
}

export interface IStoreCreditSignUpEventRuleData {
  id: string;
  appId: string;
  creditAmount: number;
  currencyCode: string;
  creditExpiryDays?: number;
}

export interface IStoreCreditLiveJoinEventRuleData {
  id: number;
  appId: string;
  streamId: string;
  streamName?: string;
  streamDescription?: string;
  thumbnailImage?: string;
  creditAmount: number;
  currencyCode: string;
  creditExpiryDays?: Date;
}

export interface ICreateStoreCreditLiveJoinEventRuleData {
  creditAmount: number;
  creditExpiryDays: number;
  currencyCode: string;
}

interface ICodeArtefact {
  id: number;
  type: 'plugins' | 'navigators';
  cdnlink: string;
  tag: string;
}

interface IFork {
  id: number;
  appId: number;
  frameworkVersion: string;
  forkName: string;
  title: string;
  publishedCommitId: string | null;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface IManifestResponse {
  id: number;
  name: string;
  uuid: string;
  organizationId: string;
  published: boolean;
  isOnboarded: boolean;
  platformType: string;
  isEditorOnboarded: boolean;
  activeBlueprintUUID: string;
  gitRepo: string;
  iosBundleId: string | null;
  androidBundleId: string | null;
  pluginsBundleId: number;
  navigatorsBundleId: number;
  createdBy: string;
  updatedBy: string;
  deletedAt: string | null;
  createdAt: string;
  updatedAt: string;
  forks: IFork[];
  codeArtefacts: ICodeArtefact[];
}

interface AppTokenUsageAttributes {
  id: string;
  appId: string;
  userId: string;
  userEmail: string;
  llmModel?: string;
  inputTokens?: number;
  outputTokens?: number;
  createdAt: Date;
  updatedAt: Date;
}

export enum TokenSource {
  PURCHASE = 'purchase',
  FREE_TIER = 'free-tier',
  PROMOTION = 'promotion',
  BONUS = 'bonus',
  REFUND = 'refund',
  ADJUSTMENT = 'adjustment',
}

interface OrganizationTokenAllocationAttributes {
  id: string;
  organizationId: string;
  tokenCount: number;
  source: TokenSource;
  expiresAt?: Date;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ITokenStats {
  totalAllocated: number;
  totalUsed: number;
  available: number;
  usageByApp: {appId: string; appName: string; tokenCount: number}[];
  usageByUser: {userId: string; userEmail: string; tokenCount: number}[];
  detailedUsage: AppTokenUsageAttributes[];
  allocationHistory: OrganizationTokenAllocationAttributes[];
}

// Paddle Types
export interface PaddlePlan {
  id: string;
  name: string;
  description: string;
  billing_cycle: {
    interval: 'month' | 'year';
    frequency: number;
  };
  unit_price: {
    amount: string;
    currency_code: string;
  };
  custom_data?: {
    credits?: string;
    popular?: boolean;
    features?: string[];
  };
}

export interface PaddleCheckoutData {
  transactionId: string;
  status: 'completed' | 'pending' | 'failed';
  planId: string;
  customData?: {
    organizationId?: string;
    userId?: string;
    credits?: string;
  };
}

export interface PaddleState {
  isInitialized: boolean;
  plans: PaddlePlan[];
  loading: boolean;
  error: string | null;
  checkoutInProgress: boolean;
}

export interface OrganizationCredits {
  id: string;
  organizationId: string;
  totalCredits: number;
  usedCredits: number;
  availableCredits: number;
  lastUpdated: Date;
}
