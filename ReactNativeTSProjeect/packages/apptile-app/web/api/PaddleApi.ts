import {AxiosPromise} from 'axios';
import {Api} from './Api';
import {PaddlePlan, PaddleCheckoutData, OrganizationCredits, Subscription, CreateSubscriptionRequest, BillingInterval} from './ApiTypes';

export default class PaddleApi {
  static baseURL = '/api/paddle';

  static getApiUrl() {
    return Api.API_SERVER + PaddleApi.baseURL;
  }

  // Fetch plans from Paddle
  static fetchPlans(): AxiosPromise<PaddlePlan[]> {
    return Api.get(PaddleApi.getApiUrl() + '/plans');
  }

  // Verify checkout completion and add credits
  static verifyCheckout(checkoutData: PaddleCheckoutData): AxiosPromise<any> {
    return Api.post(PaddleApi.getApiUrl() + '/verify-checkout', checkoutData);
  }

  // Add credits to organization
  static addCredits(
    organizationId: string,
    credits: number,
    transactionId: string,
    source: string = 'purchase',
  ): AxiosPromise<OrganizationCredits> {
    return Api.post(PaddleApi.getApiUrl() + '/add-credits', {
      organizationId,
      credits,
      transactionId,
      source,
    });
  }

  // Get organization credits
  static getOrganizationCredits(organizationId: string): AxiosPromise<OrganizationCredits> {
    return Api.get(PaddleApi.getApiUrl() + `/credits/${organizationId}`);
  }

  // Get credit usage history
  static getCreditHistory(organizationId: string): AxiosPromise<any[]> {
    return Api.get(PaddleApi.getApiUrl() + `/credits/${organizationId}/history`);
  }

  // Webhook endpoint for Paddle notifications
  static handleWebhook(webhookData: any): AxiosPromise<any> {
    return Api.post(PaddleApi.getApiUrl() + '/webhook', webhookData);
  }

  // Subscription management endpoints
  static createSubscription(data: CreateSubscriptionRequest): AxiosPromise<Subscription> {
    return Api.post(PaddleApi.getApiUrl() + '/subscribe', data);
  }

  static getOrganizationSubscriptions(organizationId: string): AxiosPromise<Subscription[]> {
    return Api.get(PaddleApi.getApiUrl() + `/subscriptions/${organizationId}`);
  }

  static cancelSubscription(subscriptionId: string, cancelAtPeriodEnd: boolean = true): AxiosPromise<Subscription> {
    return Api.post(PaddleApi.getApiUrl() + `/subscriptions/${subscriptionId}/cancel`, {
      cancelAtPeriodEnd
    });
  }
}
