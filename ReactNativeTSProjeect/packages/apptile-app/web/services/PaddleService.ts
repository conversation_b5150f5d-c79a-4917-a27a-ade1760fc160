import {initializePaddle, Paddle} from '@paddle/paddle-js';

export interface PaddlePlan {
  id: string;
  name: string;
  description: string;
  billing_cycle: {
    interval: 'month' | 'year';
    frequency: number;
  };
  unit_price: {
    amount: string;
    currency_code: string;
  };
  custom_data?: {
    credits?: string;
    popular?: boolean;
    features?: string[];
  };
}

export interface PaddleCheckoutOptions {
  items: Array<{
    priceId: string;
    quantity?: number;
  }>;
  customer?: {
    email?: string;
    id?: string;
  };
  customData?: {
    organizationId?: string;
    userId?: string;
    credits?: string;
  };
  successUrl?: string;
  settings?: {
    displayMode?: 'inline' | 'overlay';
    theme?: 'light' | 'dark';
    locale?: string;
  };
}

class PaddleService {
  private paddle: Paddle | null = null;
  private clientToken: string;
  private isInitialized: boolean = false;

  constructor(clientToken: string) {
    this.clientToken = clientToken;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      this.paddle = await initializePaddle({
        token: this.clientToken,
        environment: process.env.NODE_ENV === 'production' ? 'production' : 'sandbox',
        eventCallback: this.handlePaddleEvent.bind(this),
      });
      this.isInitialized = true;
      console.log('Paddle initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Paddle:', error);
      throw error;
    }
  }

  private handlePaddleEvent(event: any): void {
    console.log('Paddle event:', event);

    switch (event.name) {
      case 'checkout.completed':
        this.handleCheckoutCompleted(event.data);
        break;
      case 'checkout.closed':
        this.handleCheckoutClosed(event.data);
        break;
      case 'checkout.error':
        this.handleCheckoutError(event.data);
        break;
      default:
        console.log('Unhandled Paddle event:', event.name);
    }
  }

  private handleCheckoutCompleted(data: any): void {
    console.log('Checkout completed:', data);
    // Handle successful checkout
    // This will be called when payment is successful
    this.completePurchase(data);
  }

  private async completePurchase(checkoutData: any): Promise<void> {
    try {
      console.log('Completing purchase for checkout:', checkoutData);

      // Extract relevant information from checkout data
      const { customData, transactionId } = checkoutData;
      if (customData) {
        const { organizationId, planId, billingInterval } = customData;

        // Call backend to complete purchase and create subscription
        const response = await fetch('/api/paddle/complete-purchase', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            transactionId: transactionId || `paddle_${Date.now()}`,
            planId,
            organizationId,
            billingInterval
          })
        });

        if (response.ok) {
          const subscription = await response.json();
          console.log('Purchase completed successfully:', subscription);

          // Show success message
          alert(`Success! Subscription created. Tokens will be allocated ${billingInterval}.`);

          // Optionally trigger a UI update or redirect
          window.dispatchEvent(new CustomEvent('purchaseCompleted', {
            detail: subscription
          }));
        } else {
          console.error('Failed to complete purchase:', response.statusText);
          alert('Failed to complete purchase. Please contact support.');
        }
      }
    } catch (error) {
      console.error('Error completing purchase:', error);
      alert('Error completing purchase. Please try again.');
    }
  }

  private handleCheckoutClosed(data: any): void {
    console.log('Checkout closed:', data);
    // Handle checkout modal being closed
  }

  private handleCheckoutError(data: any): void {
    console.error('Checkout error:', data);
    // Handle checkout errors
  }

  async getPlans(): Promise<PaddlePlan[]> {
    if (!this.paddle) {
      throw new Error('Paddle not initialized');
    }

    try {
      // In a real implementation, you would fetch plans from Paddle API
      // For now, we'll return mock data that matches Lovable's structure
      return this.getMockPlans();
    } catch (error) {
      console.error('Failed to fetch plans:', error);
      throw error;
    }
  }

  private getMockPlans(): PaddlePlan[] {
    return [
      {
        id: 'plan_starter_monthly',
        name: 'Starter Monthly',
        description: 'Perfect for small projects',
        billing_cycle: {
          interval: 'month',
          frequency: 1,
        },
        unit_price: {
          amount: '29.00',
          currency_code: 'USD',
        },
        custom_data: {
          tokens: '10000',
          features: ['10,000 tokens/month', 'Basic support', 'API access'],
        },
      },
      {
        id: 'plan_starter_yearly',
        name: 'Starter Yearly',
        description: 'Perfect for small projects (yearly)',
        billing_cycle: {
          interval: 'year',
          frequency: 1,
        },
        unit_price: {
          amount: '290.00',
          currency_code: 'USD',
        },
        custom_data: {
          tokens: '10000',
          features: ['10,000 tokens/month', 'Basic support', 'API access', '2 months free'],
        },
      },
      {
        id: 'plan_pro_monthly',
        name: 'Pro Monthly',
        description: 'For growing businesses',
        billing_cycle: {
          interval: 'month',
          frequency: 1,
        },
        unit_price: {
          amount: '99.00',
          currency_code: 'USD',
        },
        custom_data: {
          tokens: '50000',
          popular: true,
          features: ['50,000 tokens/month', 'Priority support', 'API access', 'Advanced analytics'],
        },
      },
      {
        id: 'plan_pro_yearly',
        name: 'Pro Yearly',
        description: 'For growing businesses (yearly)',
        billing_cycle: {
          interval: 'year',
          frequency: 1,
        },
        unit_price: {
          amount: '990.00',
          currency_code: 'USD',
        },
        custom_data: {
          tokens: '50000',
          popular: true,
          features: ['50,000 tokens/month', 'Priority support', 'API access', 'Advanced analytics', '2 months free'],
        },
      },
    ];
  }

  async openCheckout(options: PaddleCheckoutOptions): Promise<void> {
    if (!this.paddle) {
      throw new Error('Paddle not initialized');
    }

    try {
      // Prepare customer data - use either ID only OR email only, not both
      let customerData = undefined;
      if (options.customer) {
        if (options.customer.id) {
          // For existing customers, use ID only
          customerData = {id: options.customer.id};
        } else if (options.customer.email) {
          // For new customers, use email only
          customerData = {email: options.customer.email};
        }
      }

      await this.paddle.Checkout.open({
        items: options.items,
        customer: customerData,
        customData: options.customData,
        settings: {
          displayMode: 'overlay',
          theme: 'light',
          locale: 'en',
          ...options.settings,
        },
      });
    } catch (error) {
      console.error('Failed to open checkout:', error);
      throw error;
    }
  }

  async getPrices(productIds: string[]): Promise<any[]> {
    if (!this.paddle) {
      throw new Error('Paddle not initialized');
    }

    try {
      // In a real implementation, you would fetch prices from Paddle API
      // For now, return mock data
      return [];
    } catch (error) {
      console.error('Failed to fetch prices:', error);
      throw error;
    }
  }
}

// Singleton instance
let paddleService: PaddleService | null = null;

export const getPaddleService = (clientToken?: string): PaddleService => {
  if (!paddleService) {
    if (!clientToken) {
      throw new Error('Client token required for first initialization');
    }
    paddleService = new PaddleService(clientToken);
  }
  return paddleService;
};

export default PaddleService;
