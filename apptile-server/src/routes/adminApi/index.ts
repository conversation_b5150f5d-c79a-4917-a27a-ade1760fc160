import { Router } from 'express';
import adminAppsRouter from './adminAppsRouter';
import adminShopRouter from './adminShopRouter';
import adminScheduledOtaRouter from './adminScheduledOtaRouter';
import adminAppsV2Router from './adminAppsV2Router';
import TokenRouter from '../TokenRouter';
import adminAppIntegrationRouter from './appIntegrationsRouter';
import adminSubscriptionRouter from './adminSubscriptionRouter';

const adminRouter = Router();
adminRouter.use('/shops', adminShopRouter);
adminRouter.use('/apps', adminAppsRouter);
adminRouter.use('/v2/apps', adminAppsV2Router);
adminRouter.use('/ota-scheduler', adminScheduledOtaRouter);
adminRouter.use('/tokens', TokenRouter);
adminRouter.use('/app-integration', adminAppIntegrationRouter);

export default adminRouter;
