import express, { Router } from 'express';
import { ResponseBuilder, logger } from '../apptile-common';
import paddleService from '../services/paddleService';
import subscriptionService from '../services/subscriptionService';

const PaddleRouter = Router();
PaddleRouter.use(express.json());

/**
 * Get available paddle plans
 * GET /api/paddle/plans
 */
PaddleRouter.get(
  '/plans',
  async (req: express.Request, res: express.Response) => {
    try {
      const plans = await paddleService.getPlans();
      return ResponseBuilder.Ok(res, plans);
    } catch (error) {
      logger.error('Error fetching paddle plans:', error);
      return ResponseBuilder.InternalServerError(res, error);
    }
  }
);

/**
 * Create a subscription
 * POST /api/paddle/subscribe
 */
PaddleRouter.post(
  '/subscribe',
  async (req: express.Request, res: express.Response) => {
    try {
      const { organizationId, planId, billingInterval } = req.body;

      if (!organizationId || !planId || !billingInterval) {
        return ResponseBuilder.BadRequest(res, 'Missing required fields: organizationId, planId, billingInterval');
      }

      const subscription = await subscriptionService.createSubscription({
        organizationId,
        planId,
        billingInterval
      });

      return ResponseBuilder.Ok(res, subscription);
    } catch (error) {
      logger.error('Error creating subscription:', error);
      if (error.message.includes('not found')) {
        return ResponseBuilder.BadRequest(res, error.message);
      }
      return ResponseBuilder.InternalServerError(res, error);
    }
  }
);

/**
 * Get organization subscriptions
 * GET /api/paddle/subscriptions/:organizationId
 */
PaddleRouter.get(
  '/subscriptions/:organizationId',
  async (req: express.Request, res: express.Response) => {
    try {
      const { organizationId } = req.params;

      if (!organizationId) {
        return ResponseBuilder.BadRequest(res, 'Missing required parameter: organizationId');
      }

      const subscriptions = await subscriptionService.getOrganizationSubscriptions(organizationId);

      return ResponseBuilder.Ok(res, subscriptions);
    } catch (error) {
      logger.error('Error getting organization subscriptions:', error);
      return ResponseBuilder.InternalServerError(res, error);
    }
  }
);

/**
 * Cancel a subscription
 * POST /api/paddle/subscriptions/:subscriptionId/cancel
 */
PaddleRouter.post(
  '/subscriptions/:subscriptionId/cancel',
  async (req: express.Request, res: express.Response) => {
    try {
      const { subscriptionId } = req.params;
      const { cancelAtPeriodEnd = true } = req.body;

      if (!subscriptionId) {
        return ResponseBuilder.BadRequest(res, 'Missing required parameter: subscriptionId');
      }

      const subscription = await subscriptionService.cancelSubscription(subscriptionId, cancelAtPeriodEnd);

      return ResponseBuilder.Ok(res, subscription);
    } catch (error) {
      logger.error('Error cancelling subscription:', error);
      if (error.message.includes('not found')) {
        return ResponseBuilder.BadRequest(res, error.message);
      }
      return ResponseBuilder.InternalServerError(res, error);
    }
  }
);

/**
 * Complete purchase and create subscription
 * POST /api/paddle/complete-purchase
 */
PaddleRouter.post(
  '/complete-purchase',
  async (req: express.Request, res: express.Response) => {
    try {
      const { transactionId, planId, organizationId, billingInterval } = req.body;

      if (!transactionId || !planId || !organizationId || !billingInterval) {
        return ResponseBuilder.BadRequest(res, 'Missing required fields: transactionId, planId, organizationId, billingInterval');
      }

      // Verify purchase with Paddle (optional but recommended)
      const isValidPurchase = await paddleService.verifyPurchase(transactionId, organizationId);

      if (!isValidPurchase) {
        logger.error('Invalid purchase verification');
        return ResponseBuilder.BadRequest(res, 'Purchase verification failed');
      }

      // Process the purchase and create subscription
      const subscription = await subscriptionService.processPurchase({
        transactionId,
        planId,
        organizationId,
        billingInterval
      });

      return ResponseBuilder.Ok(res, subscription);
    } catch (error) {
      logger.error('Error completing purchase:', error);
      if (error.message.includes('not found')) {
        return ResponseBuilder.BadRequest(res, error.message);
      }
      return ResponseBuilder.InternalServerError(res, error);
    }
  }
);

/**
 * Add credits to organization (legacy endpoint for compatibility)
 * POST /api/paddle/add-credits
 */
PaddleRouter.post(
  '/add-credits',
  async (req: express.Request, res: express.Response) => {
    try {
      const { organizationId, credits, transactionId, source = 'purchase' } = req.body;

      if (!organizationId || !credits || !transactionId) {
        return ResponseBuilder.BadRequest(res, 'Missing required fields: organizationId, credits, transactionId');
      }

      // Convert credits to tokens (assuming 1:1 ratio for now)
      const tokenCount = credits;
      
      const tokenAllocation = await subscriptionService.addTokensToOrganization(
        organizationId,
        tokenCount,
        source,
        transactionId
      );

      return ResponseBuilder.Ok(res, tokenAllocation);
    } catch (error) {
      logger.error('Error adding credits:', error);
      if (error.message.includes('not found')) {
        return ResponseBuilder.BadRequest(res, error.message);
      }
      return ResponseBuilder.InternalServerError(res, error);
    }
  }
);

/**
 * Get organization credits (legacy endpoint for compatibility)
 * GET /api/paddle/credits/:organizationId
 */
PaddleRouter.get(
  '/credits/:organizationId',
  async (req: express.Request, res: express.Response) => {
    try {
      const { organizationId } = req.params;

      if (!organizationId) {
        return ResponseBuilder.BadRequest(res, 'Missing required parameter: organizationId');
      }

      const credits = await subscriptionService.getOrganizationCredits(organizationId);

      return ResponseBuilder.Ok(res, credits);
    } catch (error) {
      logger.error('Error getting organization credits:', error);
      return ResponseBuilder.InternalServerError(res, error);
    }
  }
);

/**
 * Get credit history (legacy endpoint for compatibility)
 * GET /api/paddle/credits/:organizationId/history
 */
PaddleRouter.get(
  '/credits/:organizationId/history',
  async (req: express.Request, res: express.Response) => {
    try {
      const { organizationId } = req.params;

      if (!organizationId) {
        return ResponseBuilder.BadRequest(res, 'Missing required parameter: organizationId');
      }

      const history = await subscriptionService.getCreditHistory(organizationId);

      return ResponseBuilder.Ok(res, history);
    } catch (error) {
      logger.error('Error getting credit history:', error);
      return ResponseBuilder.InternalServerError(res, error);
    }
  }
);

export default PaddleRouter;
