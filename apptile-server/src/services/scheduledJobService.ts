import cron from 'node-cron';
import { logger } from '../apptile-common';
import subscriptionService from './subscriptionService';

/**
 * Service for managing scheduled jobs
 */
class ScheduledJobService {
  private jobs: Map<string, cron.ScheduledTask> = new Map();

  /**
   * Initialize all scheduled jobs
   */
  init(): void {
    logger.info('Initializing scheduled jobs');
    
    // Schedule periodic token allocation job
    this.schedulePeriodicTokenAllocation();
    
    logger.info('All scheduled jobs initialized');
  }

  /**
   * Schedule periodic token allocation job
   * Runs every hour to check for subscriptions that need token allocation
   */
  private schedulePeriodicTokenAllocation(): void {
    const jobName = 'periodic-token-allocation';
    
    // Run every hour at minute 0
    const task = cron.schedule('0 * * * *', async () => {
      try {
        logger.info('Running periodic token allocation job');
        await subscriptionService.processPeriodicTokenAllocation();
        logger.info('Periodic token allocation job completed successfully');
      } catch (error) {
        logger.error('Error in periodic token allocation job:', error);
      }
    }, {
      scheduled: false, // Don't start immediately
      timezone: 'UTC'
    });

    this.jobs.set(jobName, task);
    task.start();
    
    logger.info(`Scheduled job '${jobName}' initialized - runs every hour`);
  }

  /**
   * Start a specific job
   */
  startJob(jobName: string): void {
    const job = this.jobs.get(jobName);
    if (job) {
      job.start();
      logger.info(`Started job: ${jobName}`);
    } else {
      logger.warn(`Job not found: ${jobName}`);
    }
  }

  /**
   * Stop a specific job
   */
  stopJob(jobName: string): void {
    const job = this.jobs.get(jobName);
    if (job) {
      job.stop();
      logger.info(`Stopped job: ${jobName}`);
    } else {
      logger.warn(`Job not found: ${jobName}`);
    }
  }

  /**
   * Stop all jobs
   */
  stopAllJobs(): void {
    logger.info('Stopping all scheduled jobs');
    
    for (const [jobName, job] of this.jobs) {
      job.stop();
      logger.info(`Stopped job: ${jobName}`);
    }
  }

  /**
   * Get status of all jobs
   */
  getJobsStatus(): { [key: string]: boolean } {
    const status: { [key: string]: boolean } = {};
    
    for (const [jobName, job] of this.jobs) {
      status[jobName] = job.running;
    }
    
    return status;
  }

  /**
   * Manually trigger periodic token allocation
   * Useful for testing or manual execution
   */
  async triggerPeriodicTokenAllocation(): Promise<void> {
    try {
      logger.info('Manually triggering periodic token allocation');
      await subscriptionService.processPeriodicTokenAllocation();
      logger.info('Manual periodic token allocation completed successfully');
    } catch (error) {
      logger.error('Error in manual periodic token allocation:', error);
      throw error;
    }
  }
}

const scheduledJobService = new ScheduledJobService();
export default scheduledJobService;
