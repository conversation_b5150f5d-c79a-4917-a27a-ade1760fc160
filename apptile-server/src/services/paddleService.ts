import { logger } from '../apptile-common';

interface PaddlePlan {
  id: string;
  name: string;
  description: string;
  billing_cycle: {
    interval: 'month' | 'year';
    frequency: number;
  };
  unit_price: {
    amount: string;
    currency_code: string;
  };
  custom_data?: {
    credits?: string;
    tokens?: string;
    popular?: boolean;
    features?: string[];
  };
}

/**
 * Service for handling Paddle integration (simplified without webhooks)
 */
class PaddleService {

  /**
   * Get available plans from Paddle
   */
  async getPlans(): Promise<PaddlePlan[]> {
    try {
      // Mock plans for now - replace with actual Paddle API call
      const mockPlans: PaddlePlan[] = [
        {
          id: 'plan_starter_monthly',
          name: 'Starter Monthly',
          description: 'Perfect for small projects',
          billing_cycle: {
            interval: 'month',
            frequency: 1
          },
          unit_price: {
            amount: '29.00',
            currency_code: 'USD'
          },
          custom_data: {
            tokens: '10000',
            features: ['10,000 tokens/month', 'Basic support', 'API access']
          }
        },
        {
          id: 'plan_starter_yearly',
          name: 'Starter Yearly',
          description: 'Perfect for small projects (yearly)',
          billing_cycle: {
            interval: 'year',
            frequency: 1
          },
          unit_price: {
            amount: '290.00',
            currency_code: 'USD'
          },
          custom_data: {
            tokens: '10000',
            features: ['10,000 tokens/month', 'Basic support', 'API access', '2 months free']
          }
        },
        {
          id: 'plan_pro_monthly',
          name: 'Pro Monthly',
          description: 'For growing businesses',
          billing_cycle: {
            interval: 'month',
            frequency: 1
          },
          unit_price: {
            amount: '99.00',
            currency_code: 'USD'
          },
          custom_data: {
            tokens: '50000',
            popular: true,
            features: ['50,000 tokens/month', 'Priority support', 'API access', 'Advanced analytics']
          }
        },
        {
          id: 'plan_pro_yearly',
          name: 'Pro Yearly',
          description: 'For growing businesses (yearly)',
          billing_cycle: {
            interval: 'year',
            frequency: 1
          },
          unit_price: {
            amount: '990.00',
            currency_code: 'USD'
          },
          custom_data: {
            tokens: '50000',
            popular: true,
            features: ['50,000 tokens/month', 'Priority support', 'API access', 'Advanced analytics', '2 months free']
          }
        },
        {
          id: 'plan_enterprise_monthly',
          name: 'Enterprise Monthly',
          description: 'For large organizations',
          billing_cycle: {
            interval: 'month',
            frequency: 1
          },
          unit_price: {
            amount: '299.00',
            currency_code: 'USD'
          },
          custom_data: {
            tokens: '200000',
            features: ['200,000 tokens/month', '24/7 support', 'API access', 'Advanced analytics', 'Custom integrations']
          }
        },
        {
          id: 'plan_enterprise_yearly',
          name: 'Enterprise Yearly',
          description: 'For large organizations (yearly)',
          billing_cycle: {
            interval: 'year',
            frequency: 1
          },
          unit_price: {
            amount: '2990.00',
            currency_code: 'USD'
          },
          custom_data: {
            tokens: '200000',
            features: ['200,000 tokens/month', '24/7 support', 'API access', 'Advanced analytics', 'Custom integrations', '2 months free']
          }
        }
      ];

      return mockPlans;
    } catch (error) {
      logger.error('Error fetching plans from Paddle:', error);
      throw error;
    }
  }

  /**
   * Verify purchase completion (simplified - no webhook signature verification)
   */
  async verifyPurchase(transactionId: string, organizationId: string): Promise<boolean> {
    try {
      // In a real implementation, you would call Paddle API to verify the transaction
      // For now, we'll assume the purchase is valid if we have the required data
      logger.info(`Verifying purchase: ${transactionId} for organization: ${organizationId}`);

      // Mock verification - in production, call Paddle API
      return true;
    } catch (error) {
      logger.error('Error verifying purchase:', error);
      return false;
    }
  }
}

const paddleService = new PaddleService();
export default paddleService;
