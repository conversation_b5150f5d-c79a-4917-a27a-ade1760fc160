import { logger } from '../apptile-common';
import { getPaddleApiClient } from './paddleApiClient';

interface PaddlePlan {
  id: string;
  name: string;
  description: string;
  product_id: string;
  billing_cycle: {
    interval: 'month' | 'year';
    frequency: number;
  };
  unit_price: {
    amount: string;
    currency_code: string;
  };
  custom_data?: {
    tokens?: string;
    popular?: boolean;
    features?: string[];
  };
  status: 'active' | 'archived';
  product?: {
    id: string;
    name: string;
    description?: string;
    image_url?: string;
  };
}

/**
 * Service for handling Paddle integration (simplified without webhooks)
 */
class PaddleService {

  /**
   * Get available plans from Paddle
   */
  async getPlans(): Promise<PaddlePlan[]> {
    try {
      const paddleClient = getPaddleApiClient();

      // Fetch all active prices from Paddle
      const pricesResponse = await paddleClient.getPrices({
        status: 'active',
        recurring: true,
        per_page: 50
      });

      // Fetch all active products to get product details
      const productsResponse = await paddleClient.getProducts({
        status: 'active',
        per_page: 50
      });

      // Create a map of products for easy lookup
      const productsMap = new Map(
        productsResponse.data.map(product => [product.id, product])
      );

      // Transform Paddle prices to our PaddlePlan format
      const plans: PaddlePlan[] = pricesResponse.data
        .filter(price => price.billing_cycle && price.status === 'active')
        .map(price => {
          const product = productsMap.get(price.product_id);

          return {
            id: price.id,
            name: price.name || product?.name || 'Unnamed Plan',
            description: price.description || product?.description || '',
            product_id: price.product_id,
            billing_cycle: {
              interval: (price.billing_cycle.interval === 'month' || price.billing_cycle.interval === 'year')
                ? price.billing_cycle.interval
                : 'month',
              frequency: price.billing_cycle.frequency
            },
            unit_price: {
              amount: price.unit_price.amount,
              currency_code: price.unit_price.currency_code
            },
            custom_data: {
              ...price.custom_data,
              ...product?.custom_data
            },
            status: price.status,
            product: product ? {
              id: product.id,
              name: product.name,
              description: product.description,
              image_url: product.image_url
            } : undefined
          };
        })
        .sort((a, b) => {
          // Sort by price amount
          const aAmount = parseFloat(a.unit_price.amount);
          const bAmount = parseFloat(b.unit_price.amount);
          return aAmount - bAmount;
        });

      logger.info(`Fetched ${plans.length} plans from Paddle`);
      return plans;
    } catch (error) {
      logger.error('Error fetching plans from Paddle:', error);

      // Fallback to empty array or throw error based on your preference
      // For production, you might want to throw the error
      throw new Error('Failed to fetch plans from Paddle. Please try again later.');
    }
  }

  /**
   * Verify purchase completion using Paddle API
   */
  async verifyPurchase(transactionId: string, organizationId: string): Promise<{
    isValid: boolean;
    transaction?: any;
    subscription?: any;
  }> {
    try {
      logger.info(`Verifying purchase: ${transactionId} for organization: ${organizationId}`);

      const paddleClient = getPaddleApiClient();

      // Get transaction details from Paddle
      const transaction = await paddleClient.getTransaction(transactionId, {
        include: ['customer', 'address', 'business', 'discount', 'available_payment_methods']
      });

      // Verify transaction is paid
      if (transaction.status !== 'paid') {
        logger.warn(`Transaction ${transactionId} is not paid. Status: ${transaction.status}`);
        return { isValid: false, transaction };
      }

      // If transaction has a subscription, get subscription details
      let subscription = null;
      if (transaction.subscription_id) {
        try {
          subscription = await paddleClient.getSubscription(transaction.subscription_id, {
            include: ['next_transaction', 'recurring_transaction_details']
          });
        } catch (error) {
          logger.warn(`Could not fetch subscription ${transaction.subscription_id}:`, error);
        }
      }

      // Verify custom data matches organization
      const customData = transaction.custom_data;
      if (customData?.organizationId && customData.organizationId !== organizationId) {
        logger.warn(`Organization ID mismatch. Expected: ${organizationId}, Got: ${customData.organizationId}`);
        return { isValid: false, transaction };
      }

      logger.info(`Purchase verification successful for transaction: ${transactionId}`);
      return {
        isValid: true,
        transaction,
        subscription
      };
    } catch (error) {
      logger.error('Error verifying purchase:', error);
      return { isValid: false };
    }
  }

  /**
   * Get subscription details from Paddle
   */
  async getSubscriptionDetails(subscriptionId: string): Promise<any> {
    try {
      const paddleClient = getPaddleApiClient();
      return await paddleClient.getSubscription(subscriptionId, {
        include: ['next_transaction', 'recurring_transaction_details']
      });
    } catch (error) {
      logger.error(`Error fetching subscription ${subscriptionId}:`, error);
      throw error;
    }
  }

  /**
   * Cancel subscription in Paddle
   */
  async cancelSubscriptionInPaddle(subscriptionId: string, immediately: boolean = false): Promise<any> {
    try {
      const paddleClient = getPaddleApiClient();
      return await paddleClient.cancelSubscription(subscriptionId, {
        effective_from: immediately ? 'immediately' : 'next_billing_period'
      });
    } catch (error) {
      logger.error(`Error cancelling subscription ${subscriptionId}:`, error);
      throw error;
    }
  }
}

const paddleService = new PaddleService();
export default paddleService;
