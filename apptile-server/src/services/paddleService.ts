import crypto from 'crypto';
import express from 'express';
import { logger } from '../apptile-common';
import { AppConfig } from '../config';
import subscriptionService from './subscriptionService';

interface PaddlePlan {
  id: string;
  name: string;
  description: string;
  billing_cycle: {
    interval: 'month' | 'year';
    frequency: number;
  };
  unit_price: {
    amount: string;
    currency_code: string;
  };
  custom_data?: {
    credits?: string;
    tokens?: string;
    popular?: boolean;
    features?: string[];
  };
}

interface PaddleWebhookEvent {
  event_type: string;
  data: any;
}

/**
 * Service for handling Paddle integration
 */
class PaddleService {
  private webhookSecret: string;

  constructor() {
    this.webhookSecret = AppConfig.paddle?.webhookSecret || '';
  }

  /**
   * Get available plans from Paddle
   */
  async getPlans(): Promise<PaddlePlan[]> {
    try {
      // Mock plans for now - replace with actual Paddle API call
      const mockPlans: PaddlePlan[] = [
        {
          id: 'plan_starter_monthly',
          name: 'Starter Monthly',
          description: 'Perfect for small projects',
          billing_cycle: {
            interval: 'month',
            frequency: 1
          },
          unit_price: {
            amount: '29.00',
            currency_code: 'USD'
          },
          custom_data: {
            tokens: '10000',
            features: ['10,000 tokens/month', 'Basic support', 'API access']
          }
        },
        {
          id: 'plan_starter_yearly',
          name: 'Starter Yearly',
          description: 'Perfect for small projects (yearly)',
          billing_cycle: {
            interval: 'year',
            frequency: 1
          },
          unit_price: {
            amount: '290.00',
            currency_code: 'USD'
          },
          custom_data: {
            tokens: '10000',
            features: ['10,000 tokens/month', 'Basic support', 'API access', '2 months free']
          }
        },
        {
          id: 'plan_pro_monthly',
          name: 'Pro Monthly',
          description: 'For growing businesses',
          billing_cycle: {
            interval: 'month',
            frequency: 1
          },
          unit_price: {
            amount: '99.00',
            currency_code: 'USD'
          },
          custom_data: {
            tokens: '50000',
            popular: true,
            features: ['50,000 tokens/month', 'Priority support', 'API access', 'Advanced analytics']
          }
        },
        {
          id: 'plan_pro_yearly',
          name: 'Pro Yearly',
          description: 'For growing businesses (yearly)',
          billing_cycle: {
            interval: 'year',
            frequency: 1
          },
          unit_price: {
            amount: '990.00',
            currency_code: 'USD'
          },
          custom_data: {
            tokens: '50000',
            popular: true,
            features: ['50,000 tokens/month', 'Priority support', 'API access', 'Advanced analytics', '2 months free']
          }
        },
        {
          id: 'plan_enterprise_monthly',
          name: 'Enterprise Monthly',
          description: 'For large organizations',
          billing_cycle: {
            interval: 'month',
            frequency: 1
          },
          unit_price: {
            amount: '299.00',
            currency_code: 'USD'
          },
          custom_data: {
            tokens: '200000',
            features: ['200,000 tokens/month', '24/7 support', 'API access', 'Advanced analytics', 'Custom integrations']
          }
        },
        {
          id: 'plan_enterprise_yearly',
          name: 'Enterprise Yearly',
          description: 'For large organizations (yearly)',
          billing_cycle: {
            interval: 'year',
            frequency: 1
          },
          unit_price: {
            amount: '2990.00',
            currency_code: 'USD'
          },
          custom_data: {
            tokens: '200000',
            features: ['200,000 tokens/month', '24/7 support', 'API access', 'Advanced analytics', 'Custom integrations', '2 months free']
          }
        }
      ];

      return mockPlans;
    } catch (error) {
      logger.error('Error fetching plans from Paddle:', error);
      throw error;
    }
  }

  /**
   * Verify webhook signature from Paddle
   */
  async verifyWebhookSignature(req: express.Request): Promise<boolean> {
    try {
      if (!this.webhookSecret) {
        logger.warn('Paddle webhook secret not configured, skipping verification');
        return true; // Allow for development/testing
      }

      const signature = req.headers['paddle-signature'] as string;
      if (!signature) {
        return false;
      }

      const body = JSON.stringify(req.body);
      const expectedSignature = crypto
        .createHmac('sha256', this.webhookSecret)
        .update(body)
        .digest('hex');

      return signature === expectedSignature;
    } catch (error) {
      logger.error('Error verifying webhook signature:', error);
      return false;
    }
  }

  /**
   * Process incoming webhook from Paddle
   */
  async processWebhook(webhookData: PaddleWebhookEvent): Promise<void> {
    try {
      logger.info(`Processing Paddle webhook: ${webhookData.event_type}`);

      switch (webhookData.event_type) {
        case 'subscription.created':
          await this.handleSubscriptionCreated(webhookData.data);
          break;
        case 'subscription.updated':
          await this.handleSubscriptionUpdated(webhookData.data);
          break;
        case 'subscription.cancelled':
          await this.handleSubscriptionCancelled(webhookData.data);
          break;
        case 'subscription.payment_succeeded':
          await this.handlePaymentSucceeded(webhookData.data);
          break;
        case 'subscription.payment_failed':
          await this.handlePaymentFailed(webhookData.data);
          break;
        default:
          logger.info(`Unhandled webhook event type: ${webhookData.event_type}`);
      }
    } catch (error) {
      logger.error('Error processing webhook:', error);
      throw error;
    }
  }

  private async handleSubscriptionCreated(data: any): Promise<void> {
    logger.info('Handling subscription created:', data);
    // Implementation will be added in subscription service
    await subscriptionService.handleSubscriptionCreated(data);
  }

  private async handleSubscriptionUpdated(data: any): Promise<void> {
    logger.info('Handling subscription updated:', data);
    await subscriptionService.handleSubscriptionUpdated(data);
  }

  private async handleSubscriptionCancelled(data: any): Promise<void> {
    logger.info('Handling subscription cancelled:', data);
    await subscriptionService.handleSubscriptionCancelled(data);
  }

  private async handlePaymentSucceeded(data: any): Promise<void> {
    logger.info('Handling payment succeeded:', data);
    await subscriptionService.handlePaymentSucceeded(data);
  }

  private async handlePaymentFailed(data: any): Promise<void> {
    logger.info('Handling payment failed:', data);
    await subscriptionService.handlePaymentFailed(data);
  }
}

const paddleService = new PaddleService();
export default paddleService;
